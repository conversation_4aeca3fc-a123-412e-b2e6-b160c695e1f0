import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import CreateReleasePlan from '../CreateReleasePlan';

// Mock the API calls
jest.mock('@/services/ant-design-pro/api', () => ({
  createReleasePlan: jest.fn(),
  getApplications: jest.fn(() => Promise.resolve({ data: [] })),
  getApprovalFlows: jest.fn(() => Promise.resolve({ list: [] })),
}));

// Mock the ConfigDiffModal component
jest.mock('@/components/ConfigDiffModal', () => {
  return function MockConfigDiffModal() {
    return <div data-testid="config-diff-modal">Config Diff Modal</div>;
  };
});

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('CreateReleasePlan - Database Migration Tasks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should allow multiple region selection for database migration tasks', async () => {
    renderWithRouter(<CreateReleasePlan />);

    // Wait for the component to load
    await waitFor(() => {
      expect(screen.getByText('数据库迁移任务')).toBeInTheDocument();
    });

    // Find the region select dropdown
    const regionSelect = screen.getByPlaceholderText('请选择区域（可多选）');
    expect(regionSelect).toBeInTheDocument();

    // Click on the region select to open dropdown
    fireEvent.mouseDown(regionSelect);

    // Wait for options to appear
    await waitFor(() => {
      expect(screen.getByText('Indonesia')).toBeInTheDocument();
      expect(screen.getByText('Malaysia')).toBeInTheDocument();
      expect(screen.getByText('Singapore')).toBeInTheDocument();
    });

    // Select multiple regions
    fireEvent.click(screen.getByText('Indonesia'));
    fireEvent.click(screen.getByText('Malaysia'));
    fireEvent.click(screen.getByText('Singapore'));

    // Verify that multiple regions are selected
    await waitFor(() => {
      expect(screen.getByDisplayValue('ID')).toBeInTheDocument();
      expect(screen.getByDisplayValue('MY')).toBeInTheDocument();
      expect(screen.getByDisplayValue('SG')).toBeInTheDocument();
    });
  });

  test('should display selected regions correctly in the table', async () => {
    renderWithRouter(<CreateReleasePlan />);

    // Wait for the component to load
    await waitFor(() => {
      expect(screen.getByText('数据库迁移任务')).toBeInTheDocument();
    });

    // The table should show region labels when regions are selected
    // This would be tested by simulating the state change and checking the render output
    // For now, we just verify the component renders without errors
    expect(screen.getByText('区域')).toBeInTheDocument();
  });

  test('should create separate tasks for each selected region on submit', () => {
    // This test would verify that the handleSubmit function correctly
    // transforms multiple regions into separate database migration tasks
    // The actual implementation uses flatMap to create one task per region
    
    const mockTask = {
      site: 'fulfillment',
      regions: ['ID', 'MY', 'SG'],
      sql_script: 'CREATE TABLE test;'
    };

    // Simulate the transformation logic
    const result = mockTask.regions.flatMap((region: string) => ({
      site: mockTask.site,
      region: region,
      sql_script: mockTask.sql_script,
    }));

    expect(result).toHaveLength(3);
    expect(result[0]).toEqual({
      site: 'fulfillment',
      region: 'ID',
      sql_script: 'CREATE TABLE test;'
    });
    expect(result[1]).toEqual({
      site: 'fulfillment',
      region: 'MY',
      sql_script: 'CREATE TABLE test;'
    });
    expect(result[2]).toEqual({
      site: 'fulfillment',
      region: 'SG',
      sql_script: 'CREATE TABLE test;'
    });
  });
});
